{".class": "MypyFile", "_fullname": "llm_manager", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ContentFilteredException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "llm_manager.ContentFilteredException", "name": "ContentFilteredException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "llm_manager.ContentFilteredException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "llm_manager", "mro": ["llm_manager.ContentFilteredException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "llm_manager.ContentFilteredException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "llm_manager.ContentFilteredException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "LLMManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "llm_manager.LLMManager", "name": "LLMManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "llm_manager.LLMManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "llm_manager", "mro": ["llm_manager.LLMManager", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "llm_manager.LLMManager.__init__", "name": "__init__", "type": null}}, "_generate_openai_compatible": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "provider_name", "model_name", "system_prompt", "user_prompt", "temperature", "max_tokens"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "llm_manager.LLMManager._generate_openai_compatible", "name": "_generate_openai_compatible", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "provider_name", "model_name", "system_prompt", "user_prompt", "temperature", "max_tokens"], "arg_types": ["llm_manager.LLMManager", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_openai_compatible of LLMManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_siliconflow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "model_name", "system_prompt", "user_prompt", "temperature"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "llm_manager.LLMManager._generate_siliconflow", "name": "_generate_siliconflow", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "model_name", "system_prompt", "user_prompt", "temperature"], "arg_types": ["llm_manager.LLMManager", "builtins.str", "builtins.str", "builtins.str", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_siliconflow of LLMManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_initialize_providers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "llm_manager.LLMManager._initialize_providers", "name": "_initialize_providers", "type": null}}, "available_models": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "llm_manager.LLMManager.available_models", "name": "available_models", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "generate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1], "arg_names": ["self", "provider_name", "model_name", "system_prompt", "user_prompt", "temperature", "max_tokens"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "llm_manager.LLMManager.generate", "name": "generate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1], "arg_names": ["self", "provider_name", "model_name", "system_prompt", "user_prompt", "temperature", "max_tokens"], "arg_types": ["llm_manager.LLMManager", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate of LLMManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "llm_manager.LLMManager.generate", "name": "generate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1], "arg_names": ["self", "provider_name", "model_name", "system_prompt", "user_prompt", "temperature", "max_tokens"], "arg_types": ["llm_manager.LLMManager", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate of LLMManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_available_models": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "llm_manager.LLMManager.get_available_models", "name": "get_available_models", "type": null}}, "providers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "llm_manager.LLMManager.providers", "name": "providers", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "llm_manager.LLMManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "llm_manager.LLMManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "llm_manager.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "llm_manager.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "llm_manager.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "llm_manager.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "llm_manager.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "llm_manager.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "llm_manager.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "requests": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "llm_manager.requests", "name": "requests", "type": {".class": "AnyType", "missing_import_name": "llm_manager.requests", "source_any": null, "type_of_any": 3}}}, "retry": {".class": "SymbolTableNode", "cross_ref": "tenacity.retry", "kind": "Gdef"}, "settings": {".class": "SymbolTableNode", "cross_ref": "config.settings", "kind": "Gdef"}, "stop_after_attempt": {".class": "SymbolTableNode", "cross_ref": "tenacity.stop.stop_after_attempt", "kind": "Gdef"}, "wait_exponential": {".class": "SymbolTableNode", "cross_ref": "tenacity.wait.wait_exponential", "kind": "Gdef"}}, "path": "E:\\xzyxgg\\llm_manager.py"}