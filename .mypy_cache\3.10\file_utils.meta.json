{"data_mtime": 1751269622, "dep_lines": [1, 2, 3, 4, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 30, 30, 30], "dependencies": ["os", "sys", "datetime", "pathlib", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "94738892fb0b7b4415ed6f59d44bf06d6c7816bd", "id": "file_utils", "ignore_all": true, "interface_hash": "a2d1d1f0631ee7f240de92fc64df1f1cb5dd0d50", "mtime": 1750829294, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\xzyxgg\\file_utils.py", "plugin_data": null, "size": 2732, "suppressed": [], "version_id": "1.15.0"}