[{"id": "2503.14542v1", "source": "ArXiv", "title": "AI-Driven Rapid Identification of Bacterial and Fungal Pathogens in Blood Smears of Septic Patients", "abstract": "Sepsis is a life-threatening condition which requires rapid diagnosis and treatment. Traditional microbiological methods are time-consuming and expensive. In response to these challenges, deep learning algorithms were developed to identify 14 bacteria species and 3 yeast-like fungi from microscopic images of Gram-stained smears of positive blood samples from sepsis patients.   A total of 16,637 Gram-stained microscopic images were used in the study. The analysis used the Cellpose 3 model for segmentation and Attention-based Deep Multiple Instance Learning for classification. Our model achieved an accuracy of 77.15% for bacteria and 71.39% for fungi, with ROC AUC of 0.97 and 0.88, respectively. The highest values, reaching up to 96.2%, were obtained for Cutibacterium acnes, Enterococcus faecium, Stenotrophomonas maltophilia and Nakaseomyces glabratus. Classification difficulties were observed in closely related species, such as Staphylococcus hominis and Staphylococcus haemolyticus, due to morphological similarity, and within Candida albicans due to high morphotic diversity.   The study confirms the potential of our model for microbial classification, but it also indicates the need for further optimisation and expansion of the training data set. In the future, this technology could support microbial diagnosis, reducing diagnostic time and improving the effectiveness of sepsis treatment due to its simplicity and accessibility. Part of the results presented in this publication was covered by a patent application at the European Patent Office EP24461637.1 \"A computer implemented method for identifying a microorganism in a blood and a data processing system therefor\".", "authors": ["Agnieszka Sroka-Oleksiak", "<PERSON>", "<PERSON><PERSON><PERSON>", "Aldona <PERSON>-Jarząb", "Katarzyna Biegun-<PERSON><PERSON><PERSON><PERSON>ż", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Henryk Telega", "<PERSON><PERSON><PERSON>", "Monika Brzychczy-Włoch"], "journal": "ArXiv", "year": "2025", "url": "http://arxiv.org/pdf/2503.14542v1", "doi": null, "is_clinical": true, "ebm_data": {"is_clinical": true, "pico": {"intervention": "due to its simplicity"}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2407.21433v1", "source": "ArXiv", "title": "i-CardiAx: Wearable IoT-Driven System for Early Sepsis Detection Through Long-Term Vital Sign Monitoring", "abstract": "Sepsis is a significant cause of early mortality, high healthcare costs, and disability-adjusted life years. Digital interventions like continuous cardiac monitoring can help detect early warning signs and facilitate effective interventions. This paper introduces i-CardiAx, a wearable sensor utilizing low-power high-sensitivity accelerometers to measure vital signs crucial for cardiovascular health: heart rate (HR), blood pressure (BP), and respiratory rate (RR). Data collected from 10 healthy subjects using the i-CardiAx chest patch were used to develop and evaluate lightweight vital sign measurement algorithms. The algorithms demonstrated high performance: RR (-0.11 $\\pm$ 0.77 breaths\\min), HR (0.82 $\\pm$ 2.85 beats\\min), and systolic BP (-0.08 $\\pm$ 6.245 mmHg). These algorithms are embedded in an ARM Cortex-M33 processor with Bluetooth Low Energy (BLE) support, achieving inference times of 4.2 ms for HR and RR, and 8.5 ms for BP. Additionally, a multi-channel quantized Temporal Convolutional Neural (TCN) Network, trained on the open-source HiRID dataset, was developed to detect sepsis onset using digitally acquired vital signs from i-CardiAx. The quantized TCN, deployed on i-CardiAx, predicted sepsis with a median time of 8.2 hours and an energy per inference of 1.29 mJ. The i-CardiAx wearable boasts a sleep power of 0.152 mW and an average power consumption of 0.77 mW, enabling a 100 mAh battery to last approximately two weeks (432 hours) with continuous monitoring of HR, BP, and RR at 30 measurements per hour and running inference every 30 minutes. In conclusion, i-CardiAx offers an energy-efficient, high-sensitivity method for long-term cardiovascular monitoring, providing predictive alerts for sepsis and other life-threatening events.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "journal": "ArXiv", "year": "2024", "url": "http://arxiv.org/pdf/2407.21433v1", "doi": null, "is_clinical": true, "ebm_data": {"is_clinical": true, "pico": {"intervention": "s like continuous cardiac monitoring can help detect early warning signs; s; ortex-M33 processor with Bluetooth Low Energy"}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2412.03737v1", "source": "ArXiv", "title": "Utilizing Machine Learning Models to Predict Acute Kidney Injury in Septic Patients from MIMIC-III Database", "abstract": "Sepsis is a severe condition that causes the body to respond incorrectly to an infection. This reaction can subsequently cause organ failure, a major one being acute kidney injury (AKI). For septic patients, approximately 50% develop AKI, with a mortality rate above 40%. Creating models that can accurately predict AKI based on specific qualities of septic patients is crucial for early detection and intervention. Using medical data from septic patients during intensive care unit (ICU) admission from the Medical Information Mart for Intensive Care 3 (MIMIC-III) database, we extracted 3301 patients with sepsis, with 73% of patients developing AKI. The data was randomly divided into a training set (n = 1980, 40%), a test set (n = 661, 10%), and a validation set (n = 660, 50%). The proposed model was logistic regression, and it was compared against five baseline models: XGBoost, K Nearest Neighbors (KNN), Support Vector Machines (SVM), Random Forest (RF), and LightGBM. Area Under the Curve (AUC), Accuracy, F1-Score, and Recall were calculated for each model. After analysis, we were able to select 23 features to include in our model, the top features being urine output, maximum bilirubin, minimum bilirubin, weight, maximum blood urea nitrogen, and minimum estimated glomerular filtration rate. The logistic regression model performed the best, achieving an AUC score of 0.887 (95% CI: [0.861-0.915]), an accuracy of 0.817, an F1 score of 0.866, a recall score of 0.827, and a Brier score of 0.13. Compared to the best existing literature in this field, our model achieved an 8.57% improvement in AUC while using 13 fewer variables, showcasing its effectiveness in determining AKI in septic patients. While the features selected for predicting AKI in septic patients are similar to previous literature, the top features that influenced our model's performance differ.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "journal": "ArXiv", "year": "2024", "url": "http://arxiv.org/pdf/2412.03737v1", "doi": null, "is_clinical": true, "ebm_data": {"is_clinical": true, "pico": {"population": "sepsis", "comparison": "the best existing literature in this field"}, "results": {}, "study_design": null, "sample_size": 301, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2405.20523v1", "source": "ArXiv", "title": "Systems-level health of patients living with end-stage kidney disease using standard lab values", "abstract": "We present a systems-level analysis of end-stage kidney disease (ESKD) with a dynamical network analysis of 14 commonly measured blood-based biomarkers in patients undergoing regular haemodialysis. Utilizing a validated pipeline for declining homeostatic systems, our approach learns a dynamical model together with an invertible transformation that simplifies the behaviour of observed biomarkers into natural variables. Within the natural variables, we identified two distinct dynamical behaviours: (i) stochastic accumulation, the random accumulation of abnormal values, and (ii) mallostasis, a deterministic drift towards worse health. These behaviours are identified by persistent fluctuations indicating weak stability, or a gradual shift in homeostatic set point, respectively. Both lead to worsening natural variable values, making the natural variables salient survival predictors with preferred directions of increasing risk. When this worsening is transformed back into observable biomarkers, it generates a coherent spectrum of worsening medical signs characteristic of a medical syndrome. Specifically, we found that small modules of natural variables corresponded to two existing syndromes commonly afflicting ESKD patients: protein-energy wasting and sepsis. We also identified new prospective syndromes. Our findings suggest that natural variables are robust, systems-level biomarkers, capturing the complex, holistic changes in health associated with ESKD.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "journal": "ArXiv", "year": "2024", "url": "http://arxiv.org/pdf/2405.20523v1", "doi": null, "is_clinical": true, "ebm_data": {"is_clinical": true, "pico": {"outcome": "blood-based biomarkers in patients undergoing regular haemodialysis"}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2203.09999v1", "source": "ArXiv", "title": "Lens free holographic imaging for urinary tract infection screening", "abstract": "Urinary tract infections (UTIs) are a common condition that can lead to serious complications including kidney injury, altered mental status, sepsis, and death. Laboratory tests such as urinalysis and urine culture are the mainstays of UTI diagnosis, whereby a urine specimen is collected and processed to reveal its cellular and chemical composition. This process requires precise specimen collection, handling infectious human waste, controlled urine storage, and timely transportation to modern laboratory equipment for analysis. Holographic lens free imaging (LFI) can measure large volumes of urine via a simple and compact optical setup, potentially enabling automatic urine analysis at the patient bedside. We introduce an LFI system capable of resolving important urine clinical biomarkers such as red blood cells, white blood cells, crystals, casts, and E. Coli in urine phantoms. This approach is sensitive to the particulate concentrations relevant for detecting several clinical urine abnormalities such as hematuria, pyuria, and bacteriuria. We show bacteria concentrations across eight orders of magnitude can be estimated by analyzing LFI measurements. LFI measurements of blood cell concentrations are relatively insensitive to changes in bacteria concentrations of over seven orders of magnitude. Lastly, LFI reveals clear differences between UTI-positive and UTI-negative urine from human patients. Together, these results show promise for LFI as a tool for urine screening, potentially offering early, point-of-care detection of UTI and other pathological processes.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Carolina Pacheco", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "journal": "ArXiv", "year": "2022", "url": "http://arxiv.org/pdf/2203.09999v1", "doi": null, "is_clinical": true, "ebm_data": {"is_clinical": true, "pico": {"comparison": "led urine storage"}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2011.11343v3", "source": "ArXiv", "title": "A robust and generalizable immune-relatedsignature for sepsis diagnostics", "abstract": "High-throughput sequencing can detect tens of thousands of genes in parallel, providing opportunities for improving the diagnostic accuracy of multiple diseases including sepsis, which is an aggressive inflammatory response to infection that can cause organ failure and death. Early screening of sepsis is essential in clinic, but no effective diagnostic biomarkers are available yet. Here, we present a novel method, Recurrent Logistic Regression, to identify diagnostic biomarkers for sepsis from the blood transcriptome data. A panel including five immune-related genes, LRRN3, IL2RB, FCER1A, TLR5, and S100A12, are determined as diagnostic biomarkers (LIFTS) for sepsis. LIFTS discriminates patients with sepsis from normal controls in high accuracy (AUROC = 0.9959 on average; IC = [0.9722-1.0]) on nine validation cohorts across three independent platforms, which outperforms existing markers. Our analysis determined an accurate prediction model and reproducible transcriptome biomarkers that can lay a foundation for clinical diagnostic tests and biological mechanistic studies.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "journal": "ArXiv", "year": "2020", "url": "http://arxiv.org/pdf/2011.11343v3", "doi": "10.1109/TCBB.2021.3107874", "is_clinical": true, "ebm_data": {"is_clinical": true, "pico": {"population": "sepsis from normal controls in high accuracy", "comparison": "s in high accuracy"}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2009.07103v1", "source": "ArXiv", "title": "Machine learning predicts early onset of fever from continuous physiological data of critically ill patients", "abstract": "Fever can provide valuable information for diagnosis and prognosis of various diseases such as pneumonia, dengue, sepsis, etc., therefore, predicting fever early can help in the effectiveness of treatment options and expediting the treatment process. This study aims to develop novel algorithms that can accurately predict fever onset in critically ill patients by applying machine learning technique on continuous physiological data. We analyzed continuous physiological data collected every 5-minute from a cohort of over 200,000 critically ill patients admitted to an Intensive Care Unit (ICU) over a 2-year period. Each episode of fever from the same patient were considered as an independent event, with separations of at least 24 hours. We extracted descriptive statistical features from six physiological data streams, including heart rate, respiration, systolic and diastolic blood pressure, mean arterial pressure, and oxygen saturation, and use these features to independently predict the onset of fever. Using a bootstrap aggregation method, we created a balanced dataset of 7,801 afebrile and febrile patients and analyzed features up to 4 hours before the fever onset. We found that supervised machine learning methods can predict fever up to 4 hours before onset in critically ill patients with high recall, precision, and F1-score. This study demonstrates the viability of using machine learning to predict fever among hospitalized adults. The discovery of salient physiomarkers through machine learning and deep learning techniques has the potential to further accelerate the development and implementation of innovative care delivery protocols and strategies for medically vulnerable patients.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "journal": "ArXiv", "year": "2020", "url": "http://arxiv.org/pdf/2009.07103v1", "doi": null, "is_clinical": true, "ebm_data": {"is_clinical": true, "pico": {"population": "over 200; high recall", "intervention": "options; process", "outcome": "continuous physiological data collected every 5-minute from a cohort of over 200; features up to 4 hours before the fever onset"}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2004.13066v1", "source": "ArXiv", "title": "Application of Deep Interpolation Network for Clustering of Physiologic Time Series", "abstract": "Background: During the early stages of hospital admission, clinicians must use limited information to make diagnostic and treatment decisions as patient acuity evolves. However, it is common that the time series vital sign information from patients to be both sparse and irregularly collected, which poses a significant challenge for machine / deep learning techniques to analyze and facilitate the clinicians to improve the human health outcome. To deal with this problem, We propose a novel deep interpolation network to extract latent representations from sparse and irregularly sampled time-series vital signs measured within six hours of hospital admission. Methods: We created a single-center longitudinal dataset of electronic health record data for all (n=75,762) adult patient admissions to a tertiary care center lasting six hours or longer, using 55% of the dataset for training, 23% for validation, and 22% for testing. All raw time series within six hours of hospital admission were extracted for six vital signs (systolic blood pressure, diastolic blood pressure, heart rate, temperature, blood oxygen saturation, and respiratory rate). A deep interpolation network is proposed to learn from such irregular and sparse multivariate time series data to extract the fixed low-dimensional latent patterns. We use k-means clustering algorithm to clusters the patient admissions resulting into 7 clusters. Findings: Training, validation, and testing cohorts had similar age (55-57 years), sex (55% female), and admission vital signs. Seven distinct clusters were identified. M Interpretation: In a heterogeneous cohort of hospitalized patients, a deep interpolation network extracted representations from vital sign data measured within six hours of hospital admission. This approach may have important implications for clinical decision-support under time constraints and uncertainty.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Ziyuan Guan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Tezcan Ozrazgat-Baslanti", "<PERSON><PERSON><PERSON>"], "journal": "ArXiv", "year": "2020", "url": "http://arxiv.org/pdf/2004.13066v1", "doi": null, "is_clinical": true, "ebm_data": {"is_clinical": true, "pico": {"population": "hospitalized patients", "intervention": "decisions as patient acuity evolves", "outcome": "within six hours of hospital admission; within six hours of hospital admission"}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2407.02737v1", "source": "ArXiv", "title": "Development of Machine Learning Classifiers for Blood-based Diagnosis and Prognosis of Suspected Acute Infections and Sepsis", "abstract": "We applied machine learning to the unmet medical need of rapid and accurate diagnosis and prognosis of acute infections and sepsis in emergency departments. Our solution consists of a Myrna (TM) Instrument and embedded TriVerity (TM) classifiers. The instrument measures abundances of 29 messenger RNAs in patient's blood, subsequently used as features for machine learning. The classifiers convert the input features to an intuitive test report comprising the separate likelihoods of (1) a bacterial infection (2) a viral infection, and (3) severity (need for Intensive Care Unit-level care). In internal validation, the system achieved AUROC = 0.83 on the three-class disease diagnosis (bacterial, viral, or non-infected) and AUROC = 0.77 on binary prognosis of disease severity. The Myrna, TriVerity system was granted breakthrough device designation by the United States Food and Drug Administration (FDA). This engineering manuscript teaches the standard and novel machine learning methods used to translate an academic research concept to a clinical product aimed at improving patient care, and discusses lessons learned.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "journal": "ArXiv", "year": "2024", "url": "http://arxiv.org/pdf/2407.02737v1", "doi": null, "is_clinical": false, "ebm_data": {"is_clinical": false, "pico": {"comparison": null}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2501.01462v1", "source": "ArXiv", "title": "Pan-infection Foundation Framework Enables Multiple Pathogen Prediction", "abstract": "Host-response-based diagnostics can improve the accuracy of diagnosing bacterial and viral infections, thereby reducing inappropriate antibiotic prescriptions. However, the existing cohorts with limited sample size and coarse infections types are unable to support the exploration of an accurate and generalizable diagnostic model. Here, we curate the largest infection host-response transcriptome data, including 11,247 samples across 89 blood transcriptome datasets from 13 countries and 21 platforms. We build a diagnostic model for pathogen prediction starting from a pan-infection model as foundation (AUC = 0.97) based on the pan-infection dataset. Then, we utilize knowledge distillation to efficiently transfer the insights from this \"teacher\" model to four lightweight pathogen \"student\" models, i.e., staphylococcal infection (AUC = 0.99), streptococcal infection (AUC = 0.94), HIV infection (AUC = 0.93), and RSV infection (AUC = 0.94), as well as a sepsis \"student\" model (AUC = 0.99). The proposed knowledge distillation framework not only facilitates the diagnosis of pathogens using pan-infection data, but also enables an across-disease study from pan-infection to sepsis. Moreover, the framework enables high-degree lightweight design of diagnostic models, which is expected to be adaptively deployed in clinical settings.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Qitai Cai", "<PERSON>", "Qin <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "journal": "ArXiv", "year": "2024", "url": "http://arxiv.org/pdf/2501.01462v1", "doi": null, "is_clinical": false, "ebm_data": {"is_clinical": false, "pico": {}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2405.18610v1", "source": "ArXiv", "title": "DTR-Bench: An in silico Environment and Benchmark Platform for Reinforcement Learning Based Dynamic Treatment Regime", "abstract": "Reinforcement learning (RL) has garnered increasing recognition for its potential to optimise dynamic treatment regimes (DTRs) in personalised medicine, particularly for drug dosage prescriptions and medication recommendations. However, a significant challenge persists: the absence of a unified framework for simulating diverse healthcare scenarios and a comprehensive analysis to benchmark the effectiveness of RL algorithms within these contexts. To address this gap, we introduce \\textit{DTR-Bench}, a benchmarking platform comprising four distinct simulation environments tailored to common DTR applications, including cancer chemotherapy, radiotherapy, glucose management in diabetes, and sepsis treatment. We evaluate various state-of-the-art RL algorithms across these settings, particularly highlighting their performance amidst real-world challenges such as pharmacokinetic/pharmacodynamic (PK/PD) variability, noise, and missing data. Our experiments reveal varying degrees of performance degradation among RL algorithms in the presence of noise and patient variability, with some algorithms failing to converge. Additionally, we observe that using temporal observation representations does not consistently lead to improved performance in DTR settings. Our findings underscore the necessity of developing robust, adaptive RL algorithms capable of effectively managing these complexities to enhance patient-specific healthcare. We have open-sourced our benchmark and code at https://github.com/GilesLuo/DTR-Bench.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Mingcheng Zhu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "journal": "ArXiv", "year": "2024", "url": "http://arxiv.org/pdf/2405.18610v1", "doi": null, "is_clinical": false, "ebm_data": {"is_clinical": false, "pico": {"intervention": "regimes; cokinetic/pharmacodynamic"}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2203.12113v4", "source": "ArXiv", "title": "Influence of the vessel wall geometry on the wall-induced migration of red blood cells", "abstract": "The geometry of the blood vessel wall plays a regulatory role on the motion of red blood cells (RBCs). The overall topography of the vessel wall depends on many features, among which the endothelial lining of the endothelial surface layer (ESL) is an important one. The endothelial lining of vessel walls presents a large surface area for exchanging materials between blood and tissues. The ESL plays a critical role in regulating vascular permeability, hindering leukocyte adhesion as well as inhibiting coagulation during inflammation. Changes in the ESL structure are believed to cause vascular hyperpermeability and entrap immune cells during sepsis, which could significantly alter the vessel wall geometry and disturb interactions between RBCs and the vessel wall, including the wall-induced migration of RBCs and the thickening of a cell-free layer. To investigate the influence of the vessel wall geometry particularly changed by the ESL under various pathological conditions, such as sepsis, on the motion of RBCs, we developed two models to represent the ESL using the immersed boundary method in two dimensions. In particular, we used simulations to study how the lift force and drag force on a RBC near the vessel wall vary with different wall thickness, spatial variation, and permeability associated with changes in the vessel wall geometry. We find that the spatial variation of the wall has a significant effect on the wall-induced migration of the RBC for a high permeability, and that the wall-induced migration is significantly inhibited as the vessel diameter is increased.", "authors": ["<PERSON>", "<PERSON>"], "journal": "ArXiv", "year": "2022", "url": "http://arxiv.org/pdf/2203.12113v4", "doi": "10.1371/journal.pcbi.1011241", "is_clinical": false, "ebm_data": {"is_clinical": false, "pico": {}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2002.04136v2", "source": "ArXiv", "title": "The neonatal sepsis is diminished by cervical vagus nerve stimulation and tracked non-invasively by ECG: a preliminary report in the piglet model", "abstract": "An electrocardiogram (ECG)-derived heart rate variability (HRV) index reliably tracks the inflammatory response induced by low-dose lipopolysaccharide (LPS) in near-term sheep fetuses. We evaluated the effect of vagus nerve stimulation (VNS) on vagus nerve electroneurogram (VENG) and the systemic inflammatory response induced by a high dose of LPS in neonatal piglets to mimic late-onset neonatal sepsis. We tested if our HRV inflammatory index tracks inflammation in piglets and its relationship to VENG. Following anesthesia, electrodes were attached to the left vagal nerve; ECG and blood pressure (BP) were recorded throughout the experiment. Following baseline, the piglets were administered LPS as 2mg/kg IV bolus. In the VNS treated piglet, the vagus nerve was stimulated for 10 minutes prior to and 10 min after the injection of LPS. In both groups, every 15 min post LPS, the arterial blood sample was drawn for blood gas, metabolites, and inflammatory cytokines. At the end of the experiment, the piglets were euthanized. BP and HRV measures were calculated. The piglets developed a potent inflammatory response to the LPS injection with TNF-alpha, IL-1beta, IL-6 and IL-8 peaking between 45 and 90 min post-injection. VNS diminished the LPS-induced systemic inflammatory response varying across the measured cytokines from two to ten-fold. The HRV index tracked accurately the temporal profile of cytokines and VENG changes. This novel model allows manipulating and tracking neonatal sepsis: The HRV inflammatory index 1) applies across species pre- and postnatally and 2) performs well at different degrees of sepsis (i.e., nanogram and milligram doses of LPS); 3) the present VNS paradigm effectively suppresses LPS-induced inflammation, even at high doses of LPS. The potential of early postnatal VNS to counteract sepsis and of HRV monitoring to early detect and track it deserve further study.", "authors": ["Aude Castel", "<PERSON>", "<PERSON>", "Keven<PERSON> <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "journal": "ArXiv", "year": "2020", "url": "http://arxiv.org/pdf/2002.04136v2", "doi": null, "is_clinical": false, "ebm_data": {"is_clinical": false, "pico": {"intervention": "LPS as 2mg/kg IV bolus", "outcome": "the effect of vagus nerve stimulation; cytokines from two to ten-fold"}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}, {"id": "2003.12310v3", "source": "ArXiv", "title": "Optimization of Genomic Classifiers for Clinical Deployment: Evaluation of Bayesian Optimization to Select Predictive Models of Acute Infection and In-Hospital Mortality", "abstract": "Acute infection, if not rapidly and accurately detected, can lead to sepsis, organ failure and even death. Current detection of acute infection as well as assessment of a patient's severity of illness are imperfect. Characterization of a patient's immune response by quantifying expression levels of specific genes from blood represents a potentially more timely and precise means of accomplishing both tasks. Machine learning methods provide a platform to leverage this 'host response' for development of deployment-ready classification models. Prioritization of promising classifiers is dependent, in part, on hyperparameter optimization for which a number of approaches including grid search, random sampling and Bayesian optimization have been shown to be effective. We compare HO approaches for the development of diagnostic classifiers of acute infection and in-hospital mortality from gene expression of 29 diagnostic markers. We take a deployment-centered approach to our comprehensive analysis, accounting for heterogeneity in our multi-study patient cohort with our choices of dataset partitioning and hyperparameter optimization objective as well as assessing selected classifiers in external (as well as internal) validation. We find that classifiers selected by Bayesian optimization for in-hospital mortality can outperform those selected by grid search or random sampling. However, in contrast to previous research: 1) Bayesian optimization is not more efficient in selecting classifiers in all instances compared to grid search or random sampling-based methods and 2) we note marginal gains in classifier performance in only specific circumstances when using a common variant of Bayesian optimization (i.e. automatic relevance determination). Our analysis highlights the need for further practical, deployment-centered benchmarking of HO approaches in the healthcare context.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "journal": "ArXiv", "year": "2020", "url": "http://arxiv.org/pdf/2003.12310v3", "doi": null, "is_clinical": false, "ebm_data": {"is_clinical": false, "pico": {"population": "our choices of dataset partitioning", "comparison": "grid search"}, "results": {}, "study_design": null, "sample_size": null, "quality_indicators": {"randomized": null, "blinded": null, "controlled": null}}}]